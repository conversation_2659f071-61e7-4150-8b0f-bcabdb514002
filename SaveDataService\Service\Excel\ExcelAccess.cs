
using System.Data;
using System.IO;
using System;
using System.Text;
using ExcelDataReader;
using System.Security.Principal;

namespace ExcelToData
{
    public class ExcelAccess
    {

        /// <summary>
        /// 读取 Excel ; 需要添加 Excel.dll; System.Data.dll;
        /// </summary>
        /// <param name="excelName">excel文件名</param>
        /// <param name="sheetName">sheet名称</param>
        /// <returns>DataRow的集合</returns>
        public static DataRowCollection ReadExcel(string path, string sheetName)
        {
            try {
                FileStream stream = File.Open(path, FileMode.Open, FileAccess.Read, FileShare.Read);
                System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
                ExcelReaderConfiguration config = new ExcelReaderConfiguration();
                config.FallbackEncoding = Encoding.UTF8;
                IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream, config);

                DataSet result = excelReader.AsDataSet();
                //int columns = result.Tables[0].Columns.Count;
                //int rows = result.Tables[0].Rows.Count;

                //tables可以按照sheet名获取，也可以按照sheet索引获取
                //return result.Tables[0].Rows;
                return result.Tables[sheetName].Rows;
            }
            catch(Exception ex) {
                Console.WriteLine(ex.Message);
                return null;
            }
          
        }




        public static byte[] GetUTFBytes(object str)
        {
            string strs = (str == null ? "" : str.ToString());
            return Encoding.UTF8.GetBytes(strs);
        }


        public static void TypeToValue(string type, string value, BinaryWriter file)
        {
            ///可以是各种数组的存在，数组用，隔开
            if (type.IndexOf("[]") != -1)
            {
                Console.WriteLine("找到特殊的数组类型数据" + type);
            }



            switch (type)
            {
                case "string":
                    if (value == "") value = "";
                    byte[] bytes = GetUTFBytes(value);
                    if (bytes.Length > 65535)
                        throw new Exception("字符串长度超出限制 65535");
                    file.Write((ushort)bytes.Length);
                    file.Write(bytes);

                    break;
                case "byte":
                    if (value == "") value = "0";
                    file.Write((byte)Convert.ChangeType(value, typeof(byte)));
                    break;

                case "int":
                    if (value == "") value = "0";
                    file.Write((int)Convert.ChangeType(value, typeof(int)));
                    break;

                case "uint":

                    if (value == "") value = "0";
                    file.Write((uint)Convert.ChangeType(value, typeof(uint)));
                    break;

                case "float":
                    if (value == "") value = "0";
                    file.Write((Single)Convert.ChangeType(value, typeof(Single)));
                    break;
                case "bool":
                    if (value == "") value = "false";
                    value = value.ToLower();
                    if (value == "true")
                    {
                        file.Write(true);
                    }
                    else
                    {
                        file.Write(false);
                    }
                    break;
                case "vector3":
                    if (value == "") value = "0,0,0";
                    string[] t = value.Split(',');
                    file.Write((Single)Convert.ChangeType(t[0], typeof(Single)));
                    file.Write((Single)Convert.ChangeType(t[1], typeof(Single)));
                    file.Write((Single)Convert.ChangeType(t[2], typeof(Single)));
                    break;
                case "vector4":
                    if (value == "") value = "0,0,0,0";
                    string[] y = value.Split(',');
                    file.Write((Single)Convert.ChangeType(y[0], typeof(Single)));
                    file.Write((Single)Convert.ChangeType(y[1], typeof(Single)));
                    file.Write((Single)Convert.ChangeType(y[2], typeof(Single)));
                    file.Write((Single)Convert.ChangeType(y[3], typeof(Single)));
                    break;
                case "v3Array":
                    if (value == "")
                    {
                        file.Write((byte)Convert.ChangeType(0, typeof(byte)));
                    }
                    else
                    {
                        string[] v3Arr = value.Split('|');
                        file.Write((byte)Convert.ChangeType(v3Arr.Length, typeof(byte)));
                        foreach (var v3str in v3Arr)
                        {
                            string[] v3 = v3str.Split(',');
                            file.Write((Single)Convert.ChangeType(v3[0], typeof(Single)));
                            file.Write((Single)Convert.ChangeType(v3[1], typeof(Single)));
                            file.Write((Single)Convert.ChangeType(v3[2], typeof(Single)));
                        }
                    }
                    break;
                case "reshsArray":
                    if (value == "")
                    {
                        file.Write((byte)Convert.ChangeType(0, typeof(byte)));
                    }
                    else
                    {
                        string[] reshsArray = value.Split('|');
                        file.Write((byte)Convert.ChangeType(reshsArray.Length, typeof(byte)));
                        foreach (var reArray in reshsArray)
                        {
                            string[] re = reArray.Split(',');
                            file.Write((byte)Convert.ChangeType(re.Length, typeof(byte)));
                            foreach (var str in re)
                            {
                                file.Write((uint)Convert.ChangeType(str, typeof(uint)));

                            }
                        }
                    }
                    break;
                case "numArray":
                    if (value == "")
                    {
                        file.Write((byte)Convert.ChangeType(0, typeof(byte)));
                    }
                    else
                    {
                        string[] numArray = value.Split('|');
                        file.Write((byte)Convert.ChangeType(numArray.Length, typeof(byte)));
                        foreach (var numStr in numArray)
                        {
                            file.Write((uint)Convert.ChangeType(numStr, typeof(uint)));
                        }
                    }
                    break;
                case "ulong":
                    if (value == "") value = "0";
                    file.Write((ulong)Convert.ChangeType(value, typeof(ulong)));
                    break;
                case "date":
                    if (value == "") value = "0";
                    file.Write((long)Convert.ChangeType(value, typeof(long)));//时间用unix时间戳秒
                    break;
                default:
                    Console.WriteLine("有一个异常的类型传入 请程序员检查:" + type);
                    break;
            }
        }

        public static void ReadValueOfType(string type, BinaryReader br, Object obj)
        {


            if (type.IndexOf("[]") != -1)
            {
                Console.WriteLine("找到特殊的数组类型数据" + type);
                return;
            }

            switch (type)
            {
                case "string":
                    int tmpLength = br.ReadUInt16();
                    byte[] tmpArr = br.ReadBytes(tmpLength);
                    string keyStr = Encoding.UTF8.GetString(tmpArr);
                    Console.WriteLine("string=" + keyStr);
                    break;
                case "byte":
                    byte tmpByte = br.ReadByte();
                    Console.WriteLine("byte=" + tmpByte);
                    break;
                case "int":
                    int tmpInt = br.ReadInt32();
                    Console.WriteLine("int=" + tmpInt);
                    break;
                case "uint":
                    uint tmpUInt = br.ReadUInt32();
                    Console.WriteLine("uint=" + tmpUInt);
                    break;
                case "float":
                    float tmpFloat = br.ReadSingle();
                    Console.WriteLine("float=" + tmpFloat);
                    break;
                case "bool":
                    bool tmpBool = br.ReadBoolean();
                    Console.WriteLine("bool=" + tmpBool);
                    break;
                case "vector3":
                    float tmpFloat_1 = br.ReadSingle();
                    float tmpFloat_2 = br.ReadSingle();
                    float tmpFloat_3 = br.ReadSingle();
                    Console.WriteLine($"vector3={tmpFloat_1},{tmpFloat_2},{tmpFloat_3}");
                    break;
                case "vector4":
                    float tmpV4_1 = br.ReadSingle();
                    float tmpV4_2 = br.ReadSingle();
                    float tmpV4_3 = br.ReadSingle();
                    float tmpV4_4 = br.ReadSingle();
                    Console.WriteLine($"vector4={tmpV4_1},{tmpV4_2},{tmpV4_3},{tmpV4_4}");
                    break;
                case "ulong":
                    ulong tmpULong = br.ReadUInt64();
                    Console.WriteLine("ulong=" + tmpULong);
                    break;
                case "date":
                    long tmpLong = br.ReadInt64();
                    Console.WriteLine("date=" + tmpLong);
                    break;
                default:
                    Console.WriteLine("有一个异常的类型传入 请程序员检查:" + type);
                    break;


            }

        }


        public static string typeTSToReadFunctionName(string type, bool isTypescript = false)
        {
            switch (type)
            {
                case "string":
                    return "readUTFBytes";

                case "byte":
                    return "readByte";

                case "int":
                    return "readInt32";

                case "uint":
                    return "readUInt32";

                case "float":
                    return "readFloat";

                case "bool":
                    return "readBoolean";

                //case "false":
                //    return "readBoolean";
                case "vector3":
                    return "readVector3";
                case "vector4":
                    return "readVector4";
                case "v3Array":
                    return "readVector3Array";
                case "reshsArray":
                    return "readReshsArray";
                case "numArray":
                    return "readNumArray";
                default:
                    return "";
            }
        }





        public static string typeToReadFunctionName(string type, bool isTypescript = false)
        {
            switch (type)
            {
                case "string":
                    return "ReadString";

                case "byte":
                    return "ReadByte";

                case "int":
                    return "ReadInt32";

                case "uint":
                    return "ReadUInt32";

                case "float":
                    return "ReadSingle";

                case "bool":
                    return "ReadBoolean";

                case "vector3":
                    return "ReadVector3";
                case "vector4":
                    return "ReadVector4";
                case "v3Array":
                    return "ReadVector3Array";
                default:
                    return "";
            }
        }
    }
}