
using System.Data;
using System.IO;
using System;
using System.Text;
using ExcelDataReader;
using System.Security.Principal;

namespace ExcelToData
{
    public class ExcelAccess
    {

        /// <summary>
        /// 读取 Excel ; 需要添加 Excel.dll; System.Data.dll;
        /// </summary>
        /// <param name="excelName">excel文件名</param>
        /// <param name="sheetName">sheet名称</param>
        /// <returns>DataRow的集合</returns>
        public static DataRowCollection ReadExcel(string path, string sheetName)
        {
            try {
                FileStream stream = File.Open(path, FileMode.Open, FileAccess.Read, FileShare.Read);
                System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
                ExcelReaderConfiguration config = new ExcelReaderConfiguration();
                config.FallbackEncoding = Encoding.UTF8;
                IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream, config);

                DataSet result = excelReader.AsDataSet();
                //int columns = result.Tables[0].Columns.Count;
                //int rows = result.Tables[0].Rows.Count;

                //tables可以按照sheet名获取，也可以按照sheet索引获取
                //return result.Tables[0].Rows;
                return result.Tables[sheetName].Rows;
            }
            catch(Exception ex) {
                Console.WriteLine(ex.Message);
                return null;
            }
          
        }




        public static byte[] GetUTFBytes(object str)
        {
            string strs = (str == null ? "" : str.ToString());
            return Encoding.UTF8.GetBytes(strs);
        }


        public static void TypeToValue(string type, string value, BinaryWriter file)
        {
            ///可以是各种数组的存在，数组用，隔开
            if (type.IndexOf("[]") != -1)
            {
                Console.WriteLine("找到特殊的数组类型数据" + type);
                // TODO: 实现数组类型的处理
                Console.WriteLine("⚠️ 警告: 数组类型暂未完全实现，请检查数据处理逻辑");
                return;
            }

            switch (type.ToLower())
            {
                // 字符串类型 - MySQL: VARCHAR, TEXT, CHAR
                case "string":
                    if (value == "") value = "";
                    byte[] bytes = GetUTFBytes(value);
                    if (bytes.Length > 65535)
                        throw new Exception("字符串长度超出限制 65535");
                    file.Write((ushort)bytes.Length);
                    file.Write(bytes);
                    break;

                // 整数类型
                case "byte":  // MySQL: TINYINT UNSIGNED (0-255)
                    if (value == "") value = "0";
                    file.Write((byte)Convert.ChangeType(value, typeof(byte)));
                    break;

                case "sbyte":  // MySQL: TINYINT (-128 to 127)
                    if (value == "") value = "0";
                    file.Write((sbyte)Convert.ChangeType(value, typeof(sbyte)));
                    break;

                case "short":  // MySQL: SMALLINT (-32768 to 32767)
                case "int16":
                    if (value == "") value = "0";
                    file.Write((short)Convert.ChangeType(value, typeof(short)));
                    break;

                case "ushort":  // MySQL: SMALLINT UNSIGNED (0-65535)
                case "uint16":
                    if (value == "") value = "0";
                    file.Write((ushort)Convert.ChangeType(value, typeof(ushort)));
                    break;

                case "int":  // MySQL: INT (-2147483648 to 2147483647)
                case "int32":
                    if (value == "") value = "0";
                    file.Write((int)Convert.ChangeType(value, typeof(int)));
                    break;

                case "uint":  // MySQL: INT UNSIGNED (0 to 4294967295)
                case "uint32":
                    if (value == "") value = "0";
                    file.Write((uint)Convert.ChangeType(value, typeof(uint)));
                    break;

                case "long":  // MySQL: BIGINT (-9223372036854775808 to 9223372036854775807)
                case "int64":
                    if (value == "") value = "0";
                    file.Write((long)Convert.ChangeType(value, typeof(long)));
                    break;

                case "ulong":  // MySQL: BIGINT UNSIGNED (0 to 18446744073709551615)
                case "uint64":
                    if (value == "") value = "0";
                    file.Write((ulong)Convert.ChangeType(value, typeof(ulong)));
                    break;

                // 浮点数类型
                case "float":  // MySQL: FLOAT (单精度)
                case "single":
                    if (value == "") value = "0";
                    file.Write((float)Convert.ChangeType(value, typeof(float)));
                    break;

                case "double":  // MySQL: DOUBLE (双精度)
                    if (value == "") value = "0";
                    file.Write((double)Convert.ChangeType(value, typeof(double)));
                    break;

                case "decimal":  // MySQL: DECIMAL (高精度小数)
                    if (value == "") value = "0";
                    decimal decimalValue = (decimal)Convert.ChangeType(value, typeof(decimal));
                    // 将 decimal 转换为字节数组存储
                    int[] bits = decimal.GetBits(decimalValue);
                    foreach (int bit in bits)
                    {
                        file.Write(bit);
                    }
                    break;

                // 布尔类型
                case "bool":  // MySQL: TINYINT(1) 或 BOOLEAN
                case "boolean":
                    if (value == "") value = "false";
                    value = value.ToLower();
                    bool boolValue = value == "true" || value == "1" || value == "yes";
                    file.Write(boolValue);
                    break;

                // 日期时间类型
                case "datetime":  // MySQL: DATETIME
                    if (value == "") value = DateTime.MinValue.ToString();
                    DateTime dateTime = DateTime.Parse(value);
                    file.Write(dateTime.ToBinary());
                    break;

                case "timespan":  // MySQL: TIME
                    if (value == "") value = "00:00:00";
                    TimeSpan timeSpan = TimeSpan.Parse(value);
                    file.Write(timeSpan.Ticks);
                    break;

                case "date":  // 兼容原有的时间戳格式
                    if (value == "") value = "0";
                    file.Write((long)Convert.ChangeType(value, typeof(long)));//时间用unix时间戳秒
                    break;

                // GUID 类型
                case "guid":  // MySQL: CHAR(36) 或 BINARY(16)
                    if (value == "") value = Guid.Empty.ToString();
                    Guid guid = Guid.Parse(value);
                    file.Write(guid.ToByteArray());
                    break;

                // 自定义向量类型 (保持原有逻辑)
                case "vector3":
                    if (value == "") value = "0,0,0";
                    string[] t = value.Split(',');
                    if (t.Length != 3)
                        throw new Exception($"Vector3 格式错误，期望3个值，实际{t.Length}个: {value}");
                    file.Write((float)Convert.ChangeType(t[0], typeof(float)));
                    file.Write((float)Convert.ChangeType(t[1], typeof(float)));
                    file.Write((float)Convert.ChangeType(t[2], typeof(float)));
                    break;

                case "vector4":
                    if (value == "") value = "0,0,0,0";
                    string[] y = value.Split(',');
                    if (y.Length != 4)
                        throw new Exception($"Vector4 格式错误，期望4个值，实际{y.Length}个: {value}");
                    file.Write((float)Convert.ChangeType(y[0], typeof(float)));
                    file.Write((float)Convert.ChangeType(y[1], typeof(float)));
                    file.Write((float)Convert.ChangeType(y[2], typeof(float)));
                    file.Write((float)Convert.ChangeType(y[3], typeof(float)));
                    break;

                // 自定义数组类型 (保持原有逻辑)
                case "v3array":
                    if (value == "")
                    {
                        file.Write((byte)0);
                    }
                    else
                    {
                        string[] v3Arr = value.Split('|');
                        file.Write((byte)v3Arr.Length);
                        foreach (var v3str in v3Arr)
                        {
                            string[] v3 = v3str.Split(',');
                            if (v3.Length != 3)
                                throw new Exception($"V3Array 中的Vector3格式错误: {v3str}");
                            file.Write((float)Convert.ChangeType(v3[0], typeof(float)));
                            file.Write((float)Convert.ChangeType(v3[1], typeof(float)));
                            file.Write((float)Convert.ChangeType(v3[2], typeof(float)));
                        }
                    }
                    break;

                case "reshsarray":
                    if (value == "")
                    {
                        file.Write((byte)0);
                    }
                    else
                    {
                        string[] reshsArray = value.Split('|');
                        file.Write((byte)reshsArray.Length);
                        foreach (var reArray in reshsArray)
                        {
                            string[] re = reArray.Split(',');
                            file.Write((byte)re.Length);
                            foreach (var str in re)
                            {
                                file.Write((uint)Convert.ChangeType(str, typeof(uint)));
                            }
                        }
                    }
                    break;

                case "numarray":
                    if (value == "")
                    {
                        file.Write((byte)0);
                    }
                    else
                    {
                        string[] numArray = value.Split('|');
                        file.Write((byte)numArray.Length);
                        foreach (var numStr in numArray)
                        {
                            file.Write((uint)Convert.ChangeType(numStr, typeof(uint)));
                        }
                    }
                    break;

                default:
                    Console.WriteLine($"❌ 错误: 不支持的数据类型 '{type}'");
                    Console.WriteLine("📋 支持的类型列表:");
                    Console.WriteLine("   整数: byte, sbyte, short, ushort, int, uint, long, ulong");
                    Console.WriteLine("   浮点: float, double, decimal");
                    Console.WriteLine("   其他: string, bool, datetime, timespan, guid");
                    Console.WriteLine("   自定义: vector3, vector4, v3array, reshsarray, numarray");
                    Console.WriteLine("   注意: 数组类型 (type[]) 暂未完全支持");
                    throw new NotSupportedException($"不支持的数据类型: {type}");
            }
        }

        /// <summary>
        /// 从二进制读取器中读取指定类型的值
        /// </summary>
        /// <param name="type">数据类型</param>
        /// <param name="br">二进制读取器</param>
        /// <param name="obj">目标对象（暂未使用）</param>
        public static void ReadValueOfType(string type, BinaryReader br, object obj)
        {
            if (type.Contains("[]"))
            {
                Console.WriteLine("找到特殊的数组类型数据" + type);
                Console.WriteLine("⚠️ 警告: 数组类型暂未完全实现，请检查数据处理逻辑");
                return;
            }

            switch (type.ToLower())
            {
                // 字符串类型
                case "string":
                    int tmpLength = br.ReadUInt16();
                    byte[] tmpArr = br.ReadBytes(tmpLength);
                    string keyStr = Encoding.UTF8.GetString(tmpArr);
                    Console.WriteLine("string=" + keyStr);
                    break;

                // 整数类型
                case "byte":
                    byte tmpByte = br.ReadByte();
                    Console.WriteLine("byte=" + tmpByte);
                    break;

                case "sbyte":
                    sbyte tmpSByte = br.ReadSByte();
                    Console.WriteLine("sbyte=" + tmpSByte);
                    break;

                case "short":
                case "int16":
                    short tmpShort = br.ReadInt16();
                    Console.WriteLine("short=" + tmpShort);
                    break;

                case "ushort":
                case "uint16":
                    ushort tmpUShort = br.ReadUInt16();
                    Console.WriteLine("ushort=" + tmpUShort);
                    break;

                case "int":
                case "int32":
                    int tmpInt = br.ReadInt32();
                    Console.WriteLine("int=" + tmpInt);
                    break;

                case "uint":
                case "uint32":
                    uint tmpUInt = br.ReadUInt32();
                    Console.WriteLine("uint=" + tmpUInt);
                    break;

                case "long":
                case "int64":
                    long tmpLong = br.ReadInt64();
                    Console.WriteLine("long=" + tmpLong);
                    break;

                case "ulong":
                case "uint64":
                    ulong tmpULong = br.ReadUInt64();
                    Console.WriteLine("ulong=" + tmpULong);
                    break;

                // 浮点数类型
                case "float":
                case "single":
                    float tmpFloat = br.ReadSingle();
                    Console.WriteLine("float=" + tmpFloat);
                    break;

                case "double":
                    double tmpDouble = br.ReadDouble();
                    Console.WriteLine("double=" + tmpDouble);
                    break;

                case "decimal":
                    // 读取 decimal 的四个 int 组件
                    int[] bits = new int[4];
                    for (int i = 0; i < 4; i++)
                    {
                        bits[i] = br.ReadInt32();
                    }
                    decimal tmpDecimal = new decimal(bits);
                    Console.WriteLine("decimal=" + tmpDecimal);
                    break;

                // 布尔类型
                case "bool":
                case "boolean":
                    bool tmpBool = br.ReadBoolean();
                    Console.WriteLine("bool=" + tmpBool);
                    break;

                // 日期时间类型
                case "datetime":
                    long dateTimeBinary = br.ReadInt64();
                    DateTime tmpDateTime = DateTime.FromBinary(dateTimeBinary);
                    Console.WriteLine("datetime=" + tmpDateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                    break;

                case "timespan":
                    long timeSpanTicks = br.ReadInt64();
                    TimeSpan tmpTimeSpan = new TimeSpan(timeSpanTicks);
                    Console.WriteLine("timespan=" + tmpTimeSpan.ToString());
                    break;

                case "date":  // 兼容原有的时间戳格式
                    long tmpDateLong = br.ReadInt64();
                    Console.WriteLine("date=" + tmpDateLong);
                    break;

                // GUID 类型
                case "guid":
                    byte[] guidBytes = br.ReadBytes(16);
                    Guid tmpGuid = new Guid(guidBytes);
                    Console.WriteLine("guid=" + tmpGuid.ToString());
                    break;

                // 自定义向量类型
                case "vector3":
                    float tmpFloat_1 = br.ReadSingle();
                    float tmpFloat_2 = br.ReadSingle();
                    float tmpFloat_3 = br.ReadSingle();
                    Console.WriteLine($"vector3={tmpFloat_1},{tmpFloat_2},{tmpFloat_3}");
                    break;

                case "vector4":
                    float tmpV4_1 = br.ReadSingle();
                    float tmpV4_2 = br.ReadSingle();
                    float tmpV4_3 = br.ReadSingle();
                    float tmpV4_4 = br.ReadSingle();
                    Console.WriteLine($"vector4={tmpV4_1},{tmpV4_2},{tmpV4_3},{tmpV4_4}");
                    break;

                default:
                    Console.WriteLine($"❌ 错误: 不支持的数据类型 '{type}' 在读取操作中");
                    Console.WriteLine("📋 支持的类型列表:");
                    Console.WriteLine("   整数: byte, sbyte, short, ushort, int, uint, long, ulong");
                    Console.WriteLine("   浮点: float, double, decimal");
                    Console.WriteLine("   其他: string, bool, datetime, timespan, guid");
                    Console.WriteLine("   自定义: vector3, vector4");
                    break;
            }
        }


        /// <summary>
        /// 获取TypeScript读取函数名称
        /// </summary>
        /// <param name="type">数据类型</param>
        /// <param name="isTypescript">是否为TypeScript（暂未使用）</param>
        /// <returns>对应的TypeScript读取函数名</returns>
        public static string TypeScriptToReadFunctionName(string type, bool isTypescript = false)
        {
            switch (type.ToLower())
            {
                // 基础类型
                case "string":
                    return "readUTFBytes";
                case "byte":
                case "sbyte":
                    return "readByte";
                case "short":
                case "int16":
                    return "readInt16";
                case "ushort":
                case "uint16":
                    return "readUInt16";
                case "int":
                case "int32":
                    return "readInt32";
                case "uint":
                case "uint32":
                    return "readUInt32";
                case "long":
                case "int64":
                    return "readInt64";
                case "ulong":
                case "uint64":
                    return "readUInt64";
                case "float":
                case "single":
                    return "readFloat";
                case "double":
                    return "readDouble";
                case "decimal":
                    return "readDecimal";
                case "bool":
                case "boolean":
                    return "readBoolean";
                case "datetime":
                    return "readDateTime";
                case "timespan":
                    return "readTimeSpan";
                case "guid":
                    return "readGuid";
                case "date":
                    return "readDate";

                // 自定义类型
                case "vector3":
                    return "readVector3";
                case "vector4":
                    return "readVector4";
                case "v3array":
                    return "readVector3Array";
                case "reshsarray":
                    return "readReshsArray";
                case "numarray":
                    return "readNumArray";

                default:
                    Console.WriteLine($"⚠️ 警告: 未知的TypeScript读取函数类型: {type}");
                    return "";
            }
        }





        /// <summary>
        /// 获取C#读取函数名称
        /// </summary>
        /// <param name="type">数据类型</param>
        /// <param name="isTypescript">是否为TypeScript（暂未使用）</param>
        /// <returns>对应的C#读取函数名</returns>
        public static string TypeToReadFunctionName(string type, bool isTypescript = false)
        {
            switch (type.ToLower())
            {
                // 基础类型
                case "string":
                    return "ReadString";
                case "byte":
                    return "ReadByte";
                case "sbyte":
                    return "ReadSByte";
                case "short":
                case "int16":
                    return "ReadInt16";
                case "ushort":
                case "uint16":
                    return "ReadUInt16";
                case "int":
                case "int32":
                    return "ReadInt32";
                case "uint":
                case "uint32":
                    return "ReadUInt32";
                case "long":
                case "int64":
                    return "ReadInt64";
                case "ulong":
                case "uint64":
                    return "ReadUInt64";
                case "float":
                case "single":
                    return "ReadSingle";
                case "double":
                    return "ReadDouble";
                case "decimal":
                    return "ReadDecimal";
                case "bool":
                case "boolean":
                    return "ReadBoolean";
                case "datetime":
                    return "ReadDateTime";
                case "timespan":
                    return "ReadTimeSpan";
                case "guid":
                    return "ReadGuid";
                case "date":
                    return "ReadInt64"; // 兼容原有的时间戳格式

                // 自定义类型
                case "vector3":
                    return "ReadVector3";
                case "vector4":
                    return "ReadVector4";
                case "v3array":
                    return "ReadVector3Array";
                case "reshsarray":
                    return "ReadReshsArray";
                case "numarray":
                    return "ReadNumArray";

                default:
                    Console.WriteLine($"⚠️ 警告: 未知的C#读取函数类型: {type}");
                    return "";
            }
        }
    }
}